"""
专业游戏动画师Blender脚本
Professional Game Animator Blender Script
支持初级和中级动画师功能：动捕清理、走跑跳、打击感、特技动作
"""

# Blender-specific imports - only available in Blender environment
try:
    import bpy  # type: ignore
    import bmesh  # type: ignore
    import mathutils  # type: ignore
    from mathutils import Vector, Euler, Quaternion  # type: ignore
    BLENDER_AVAILABLE = True
    BlenderObject = bpy.types.Object
except ImportError:
    print("Warning: Blender modules not available. This script must be run within Blender.")
    BLENDER_AVAILABLE = False
    # Create mock objects for type checking
    bpy = None
    bmesh = None
    mathutils = None
    Vector = None
    Euler = None
    Quaternion = None
    BlenderObject = Any

import json
import sys
import os
from typing import Dict, List, Any, Optional, TYPE_CHECKING
import argparse
import math

# Handle numpy import
try:
    import numpy as np
except ImportError:
    print("Warning: numpy not available")
    np = None

# Type checking imports
if TYPE_CHECKING and not BLENDER_AVAILABLE:
    from typing import Any as BlenderObject


class ProfessionalBlenderAnimator:
    """
    专业游戏动画师Blender类
    Professional Game Animator Blender Class
    """

    def __init__(self):
        self.scene = bpy.context.scene
        self.frame_rate = 30  # 游戏标准帧率
        self.current_frame = 1

        # 清理场景
        self.clear_scene()

        # 专业动画预设
        self.animation_presets = self._load_professional_presets()

        # 动画师工具
        self.junior_animator = JuniorAnimatorTools()
        self.intermediate_animator = IntermediateAnimatorTools()

        print("Professional Blender Animator initialized")
    
    def clear_scene(self):
        """Clear all mesh objects from the scene"""
        bpy.ops.object.select_all(action='SELECT')
        bpy.ops.object.delete(use_global=False, confirm=False)
        
        # Keep camera and lights
        bpy.ops.object.camera_add(location=(7, -7, 5))
        bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    
    def _load_professional_presets(self) -> Dict[str, Dict[str, Any]]:
        """Load predefined animation presets"""
        return {
            'walk': {
                'keyframes': [
                    {'frame': 0, 'location': (0, 0, 0), 'rotation': (0, 0, 0)},
                    {'frame': 12, 'location': (0, 1, 0), 'rotation': (0, 0, 0)},
                    {'frame': 24, 'location': (0, 2, 0), 'rotation': (0, 0, 0)}
                ],
                'bone_animations': {
                    'leg.L': [
                        {'frame': 0, 'rotation': (0, 0, 0)},
                        {'frame': 6, 'rotation': (0.5, 0, 0)},
                        {'frame': 12, 'rotation': (0, 0, 0)},
                        {'frame': 18, 'rotation': (-0.5, 0, 0)},
                        {'frame': 24, 'rotation': (0, 0, 0)}
                    ],
                    'leg.R': [
                        {'frame': 0, 'rotation': (0, 0, 0)},
                        {'frame': 6, 'rotation': (-0.5, 0, 0)},
                        {'frame': 12, 'rotation': (0, 0, 0)},
                        {'frame': 18, 'rotation': (0.5, 0, 0)},
                        {'frame': 24, 'rotation': (0, 0, 0)}
                    ]
                }
            },
            'wave': {
                'bone_animations': {
                    'arm.R': [
                        {'frame': 0, 'rotation': (0, 0, 0)},
                        {'frame': 6, 'rotation': (0, 0, 1.5)},
                        {'frame': 12, 'rotation': (0, 0, 1.2)},
                        {'frame': 18, 'rotation': (0, 0, 1.5)},
                        {'frame': 24, 'rotation': (0, 0, 0)}
                    ],
                    'hand.R': [
                        {'frame': 0, 'rotation': (0, 0, 0)},
                        {'frame': 6, 'rotation': (0, 0.3, 0)},
                        {'frame': 12, 'rotation': (0, -0.3, 0)},
                        {'frame': 18, 'rotation': (0, 0.3, 0)},
                        {'frame': 24, 'rotation': (0, 0, 0)}
                    ]
                }
            },
            'jump': {
                'keyframes': [
                    {'frame': 0, 'location': (0, 0, 0), 'rotation': (0, 0, 0)},
                    {'frame': 6, 'location': (0, 0, 0.5), 'rotation': (0, 0, 0)},
                    {'frame': 12, 'location': (0, 0, 2), 'rotation': (0, 0, 0)},
                    {'frame': 18, 'location': (0, 0, 0.5), 'rotation': (0, 0, 0)},
                    {'frame': 24, 'location': (0, 0, 0), 'rotation': (0, 0, 0)}
                ]
            },
            'sit': {
                'keyframes': [
                    {'frame': 0, 'location': (0, 0, 0), 'rotation': (0, 0, 0)},
                    {'frame': 24, 'location': (0, 0, -1), 'rotation': (0, 0, 0)}
                ],
                'bone_animations': {
                    'spine': [
                        {'frame': 0, 'rotation': (0, 0, 0)},
                        {'frame': 24, 'rotation': (0.3, 0, 0)}
                    ]
                }
            }
        }
    
    def load_character_model(self, model_path: str) -> Optional[BlenderObject]:
        """Load character model from file"""
        try:
            if model_path.endswith('.blend'):
                # Load from .blend file
                with bpy.data.libraries.load(model_path) as (data_from, data_to):
                    data_to.objects = data_from.objects
                
                # Link objects to scene
                for obj in data_to.objects:
                    if obj is not None:
                        bpy.context.collection.objects.link(obj)
                        if obj.type == 'ARMATURE':
                            return obj
            
            elif model_path.endswith(('.fbx', '.obj')):
                # Import FBX or OBJ
                if model_path.endswith('.fbx'):
                    bpy.ops.import_scene.fbx(filepath=model_path)
                else:
                    bpy.ops.import_scene.obj(filepath=model_path)
                
                # Find armature object
                for obj in bpy.context.scene.objects:
                    if obj.type == 'ARMATURE':
                        return obj
            
            return None
            
        except Exception as e:
            print(f"Error loading character model: {e}")
            return None
    
    def create_default_character(self) -> bpy.types.Object:
        """Create a simple default character if no model is provided"""
        # Create a simple humanoid figure using basic shapes
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
        body = bpy.context.active_object
        body.name = "Body"
        
        # Add armature
        bpy.ops.object.armature_add(location=(0, 0, 0))
        armature = bpy.context.active_object
        armature.name = "Character_Armature"
        
        return armature
    
    def generate_animation(self, motion_data: Dict[str, Any]) -> bool:
        """
        Generate animation from motion data
        """
        try:
            # Load or create character
            character_path = motion_data.get('character_model_path')
            if character_path and os.path.exists(character_path):
                character = self.load_character_model(character_path)
            else:
                character = self.create_default_character()
            
            if not character:
                print("Failed to load or create character")
                return False
            
            # Set frame rate
            self.scene.frame_set(1)
            self.scene.frame_start = 1
            
            # Process action sequence
            action_sequence = motion_data.get('action_sequence', {})
            actions = action_sequence.get('actions', [])
            
            total_frames = 0
            current_frame = 1
            
            for action in actions:
                frames_for_action = int(action.get('duration', 2.0) * self.frame_rate)
                self.animate_action(character, action, current_frame, frames_for_action)
                current_frame += frames_for_action
                total_frames += frames_for_action
            
            # Set scene end frame
            self.scene.frame_end = total_frames
            
            return True
            
        except Exception as e:
            print(f"Error generating animation: {e}")
            return False
    
    def animate_action(self, character: bpy.types.Object, action: Dict[str, Any], 
                      start_frame: int, duration_frames: int):
        """Animate a specific action"""
        action_name = action.get('name', 'idle')
        action_type = action.get('type', 'pose')
        parameters = action.get('parameters', {})
        
        # Get animation preset
        preset = self.animation_presets.get(action_name, {})
        
        if not preset:
            # Create basic idle animation
            self.create_idle_animation(character, start_frame, duration_frames)
            return
        
        # Apply keyframe animations
        if 'keyframes' in preset:
            self.apply_keyframes(character, preset['keyframes'], start_frame, duration_frames)
        
        # Apply bone animations if character has armature
        if character.type == 'ARMATURE' and 'bone_animations' in preset:
            self.apply_bone_animations(character, preset['bone_animations'], 
                                     start_frame, duration_frames, parameters)
    
    def apply_keyframes(self, obj: bpy.types.Object, keyframes: List[Dict], 
                       start_frame: int, duration_frames: int):
        """Apply keyframe animations to object"""
        for keyframe in keyframes:
            frame = start_frame + int(keyframe['frame'] * duration_frames / 24)
            
            # Set location
            if 'location' in keyframe:
                obj.location = keyframe['location']
                obj.keyframe_insert(data_path="location", frame=frame)
            
            # Set rotation
            if 'rotation' in keyframe:
                obj.rotation_euler = keyframe['rotation']
                obj.keyframe_insert(data_path="rotation_euler", frame=frame)
    
    def apply_bone_animations(self, armature: bpy.types.Object, bone_animations: Dict, 
                            start_frame: int, duration_frames: int, parameters: Dict):
        """Apply animations to specific bones"""
        # Enter pose mode
        bpy.context.view_layer.objects.active = armature
        bpy.ops.object.mode_set(mode='POSE')
        
        for bone_name, keyframes in bone_animations.items():
            if bone_name in armature.pose.bones:
                bone = armature.pose.bones[bone_name]
                
                for keyframe in keyframes:
                    frame = start_frame + int(keyframe['frame'] * duration_frames / 24)
                    
                    # Apply intensity modifier
                    intensity = parameters.get('intensity', 'normal')
                    intensity_multiplier = {'slow': 0.7, 'normal': 1.0, 'strong': 1.3}.get(intensity, 1.0)
                    
                    if 'rotation' in keyframe:
                        rotation = [r * intensity_multiplier for r in keyframe['rotation']]
                        bone.rotation_euler = rotation
                        bone.keyframe_insert(data_path="rotation_euler", frame=frame)
        
        # Return to object mode
        bpy.ops.object.mode_set(mode='OBJECT')
    
    def create_idle_animation(self, character: bpy.types.Object, start_frame: int, duration_frames: int):
        """Create a basic idle animation"""
        # Simple breathing animation
        for frame in range(start_frame, start_frame + duration_frames, 6):
            scale_factor = 1.0 + 0.02 * (frame % 12 - 6) / 6
            character.scale = (1.0, 1.0, scale_factor)
            character.keyframe_insert(data_path="scale", frame=frame)
    
    def export_animation(self, output_path: str, format: str = 'fbx') -> bool:
        """Export the generated animation"""
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Select all objects
            bpy.ops.object.select_all(action='SELECT')
            
            if format.lower() == 'fbx':
                bpy.ops.export_scene.fbx(
                    filepath=output_path,
                    use_selection=True,
                    bake_anim=True,
                    bake_anim_use_all_bones=True,
                    bake_anim_use_nla_strips=False,
                    bake_anim_use_all_actions=False
                )
            elif format.lower() == 'gltf':
                bpy.ops.export_scene.gltf(
                    filepath=output_path,
                    use_selection=True,
                    export_animations=True
                )
            else:
                print(f"Unsupported export format: {format}")
                return False
            
            print(f"Animation exported to: {output_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting animation: {e}")
            return False


class JuniorAnimatorTools:
    """初级动画师工具类"""

    def __init__(self):
        self.frame_rate = 30

    def clean_motion_capture_data(self, armature_obj):
        """清理动捕数据"""
        if not armature_obj or armature_obj.type != 'ARMATURE':
            return False

        # 选择骨架对象
        bpy.context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode='POSE')

        # 应用平滑滤波器
        bpy.ops.pose.select_all(action='SELECT')

        # 简化关键帧
        bpy.ops.pose.select_all(action='SELECT')
        for bone in armature_obj.pose.bones:
            bone.bone.select = True

        # 应用噪声减少
        if armature_obj.animation_data and armature_obj.animation_data.action:
            action = armature_obj.animation_data.action
            for fcurve in action.fcurves:
                # 简单的关键帧简化
                bpy.context.scene.frame_set(1)
                fcurve.select = True

        bpy.ops.object.mode_set(mode='OBJECT')
        print("Motion capture data cleaned")
        return True


class IntermediateAnimatorTools:
    """中级动画师工具类"""

    def __init__(self):
        self.frame_rate = 30

    def create_combat_attack_keyframes(self, armature_obj, attack_type, start_frame, end_frame):
        """创建战斗攻击关键帧"""
        if not armature_obj:
            return

        bpy.context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode='POSE')

        duration = end_frame - start_frame
        windup_frame = start_frame + duration // 4
        impact_frame = start_frame + duration // 2
        recovery_frame = end_frame - duration // 4

        if attack_type == "punch":
            self._create_punch_animation(armature_obj, start_frame, windup_frame, impact_frame, recovery_frame, end_frame)
        elif attack_type == "kick":
            self._create_kick_animation(armature_obj, start_frame, windup_frame, impact_frame, recovery_frame, end_frame)

        bpy.ops.object.mode_set(mode='OBJECT')
        print(f"Combat attack '{attack_type}' created from frame {start_frame} to {end_frame}")

    def _create_punch_animation(self, armature_obj, start_frame, windup_frame, impact_frame, recovery_frame, end_frame):
        """创建拳击动画"""
        if 'arm.R' in armature_obj.pose.bones:
            right_arm = armature_obj.pose.bones['arm.R']

            # 起始姿态
            bpy.context.scene.frame_set(start_frame)
            right_arm.rotation_euler = (0, 0, 0)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 蓄力
            bpy.context.scene.frame_set(windup_frame)
            right_arm.rotation_euler = (-0.5, 0, -0.3)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 击中
            bpy.context.scene.frame_set(impact_frame)
            right_arm.rotation_euler = (0.3, 0, 0.5)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 恢复
            bpy.context.scene.frame_set(recovery_frame)
            right_arm.rotation_euler = (0.1, 0, 0.2)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 结束
            bpy.context.scene.frame_set(end_frame)
            right_arm.rotation_euler = (0, 0, 0)
            right_arm.keyframe_insert(data_path="rotation_euler")

    def create_backflip_720_keyframes(self, armature_obj, start_frame, end_frame):
        """创建720度后空翻关键帧"""
        if not armature_obj:
            return

        bpy.context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode='POSE')

        duration = end_frame - start_frame
        quarter_2 = start_frame + duration // 2

        # 根骨骼旋转（如果存在）
        if armature_obj.pose.bones:
            root_bone = armature_obj.pose.bones[0]  # 假设第一个是根骨骼

            # 起始
            bpy.context.scene.frame_set(start_frame)
            root_bone.rotation_euler = (0, 0, 0)
            root_bone.keyframe_insert(data_path="rotation_euler")

            # 第一个360度
            bpy.context.scene.frame_set(quarter_2)
            root_bone.rotation_euler = (math.pi * 2, 0, 0)  # 360度
            root_bone.keyframe_insert(data_path="rotation_euler")

            # 第二个360度
            bpy.context.scene.frame_set(end_frame)
            root_bone.rotation_euler = (math.pi * 4, 0, 0)  # 720度
            root_bone.keyframe_insert(data_path="rotation_euler")

        bpy.ops.object.mode_set(mode='OBJECT')
        print(f"720-degree backflip created from frame {start_frame} to {end_frame}")


def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Generate Blender animation from motion data')
    parser.add_argument('--input', required=True, help='Input JSON file with motion data')
    parser.add_argument('--output', required=True, help='Output animation file path')
    parser.add_argument('--format', default='fbx', help='Export format (fbx, gltf)')
    
    # Parse arguments (skip Blender's arguments)
    argv = sys.argv
    if "--" in argv:
        argv = argv[argv.index("--") + 1:]
    else:
        argv = []
    
    args = parser.parse_args(argv)
    
    # Load motion data
    try:
        with open(args.input, 'r') as f:
            motion_data = json.load(f)
    except Exception as e:
        print(f"Error loading motion data: {e}")
        return
    
    # Generate animation
    generator = ProfessionalBlenderAnimator()
    
    if generator.generate_animation(motion_data):
        if generator.export_animation(args.output, args.format):
            print("Animation generation completed successfully!")
        else:
            print("Failed to export animation")
    else:
        print("Failed to generate animation")


if __name__ == "__main__":
    main()
