"""
Blender utilities and compatibility layer
Handles imports and provides fallbacks when not running in Blender
"""

import sys
from typing import Any, Optional, TYPE_CHECKING

# Check if we're running in Blender
BLENDER_AVAILABLE = False
try:
    import bpy
    import bmesh
    import mathutils
    BLENDER_AVAILABLE = True
except ImportError:
    # We're not in Blender, use type stubs for development
    if TYPE_CHECKING:
        import bpy
        import bmesh
        import mathutils
    else:
        # Create mock modules for runtime
        bpy = None
        bmesh = None
        mathutils = None

def is_blender_available() -> bool:
    """Check if Blender modules are available"""
    return BLENDER_AVAILABLE

def require_blender() -> None:
    """Raise an error if Blender is not available"""
    if not BLENDER_AVAILABLE:
        raise RuntimeError(
            "This function requires Blender. "
            "Please run this script within Blender's Python environment."
        )

def get_blender_version() -> Optional[str]:
    """Get Blender version if available"""
    if BLENDER_AVAILABLE and bpy:
        return ".".join(map(str, bpy.app.version))
    return None

class BlenderContext:
    """Context manager for Blender operations"""
    
    def __enter__(self):
        require_blender()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

def safe_blender_import():
    """
    Safely import Blender modules with proper error handling
    Returns tuple of (bpy, bmesh, mathutils) or (None, None, None)
    """
    try:
        import bpy
        import bmesh
        import mathutils
        return bpy, bmesh, mathutils
    except ImportError:
        return None, None, None

# Convenience functions for common Blender operations
def clear_scene():
    """Clear all objects from the scene (Blender only)"""
    if not BLENDER_AVAILABLE:
        print("Warning: clear_scene() called outside Blender environment")
        return
    
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False, confirm=False)

def create_camera(location=(7, -7, 5)):
    """Create a camera (Blender only)"""
    if not BLENDER_AVAILABLE:
        print("Warning: create_camera() called outside Blender environment")
        return None
    
    bpy.ops.object.camera_add(location=location)
    return bpy.context.active_object

def create_light(light_type='SUN', location=(5, 5, 10)):
    """Create a light (Blender only)"""
    if not BLENDER_AVAILABLE:
        print("Warning: create_light() called outside Blender environment")
        return None
    
    bpy.ops.object.light_add(type=light_type, location=location)
    return bpy.context.active_object

def export_fbx(filepath: str, **kwargs):
    """Export scene as FBX (Blender only)"""
    if not BLENDER_AVAILABLE:
        print(f"Warning: export_fbx() called outside Blender environment. Would export to: {filepath}")
        return False
    
    try:
        bpy.ops.export_scene.fbx(filepath=filepath, **kwargs)
        return True
    except Exception as e:
        print(f"Error exporting FBX: {e}")
        return False

def export_gltf(filepath: str, **kwargs):
    """Export scene as glTF (Blender only)"""
    if not BLENDER_AVAILABLE:
        print(f"Warning: export_gltf() called outside Blender environment. Would export to: {filepath}")
        return False
    
    try:
        bpy.ops.export_scene.gltf(filepath=filepath, **kwargs)
        return True
    except Exception as e:
        print(f"Error exporting glTF: {e}")
        return False

# Vector and math utilities that work outside Blender
class MockVector:
    """Mock Vector class for development outside Blender"""
    
    def __init__(self, coords=(0.0, 0.0, 0.0)):
        self.coords = list(coords)
    
    @property
    def x(self):
        return self.coords[0] if len(self.coords) > 0 else 0.0
    
    @property
    def y(self):
        return self.coords[1] if len(self.coords) > 1 else 0.0
    
    @property
    def z(self):
        return self.coords[2] if len(self.coords) > 2 else 0.0
    
    def __repr__(self):
        return f"MockVector({self.coords})"

def create_vector(coords=(0.0, 0.0, 0.0)):
    """Create a vector (Blender or mock)"""
    if BLENDER_AVAILABLE and mathutils:
        return mathutils.Vector(coords)
    else:
        return MockVector(coords)

def create_euler(angles=(0.0, 0.0, 0.0), order='XYZ'):
    """Create an Euler rotation (Blender or mock)"""
    if BLENDER_AVAILABLE and mathutils:
        return mathutils.Euler(angles, order)
    else:
        return {"angles": angles, "order": order}

def create_quaternion(wxyz=(1.0, 0.0, 0.0, 0.0)):
    """Create a quaternion (Blender or mock)"""
    if BLENDER_AVAILABLE and mathutils:
        return mathutils.Quaternion(wxyz)
    else:
        return {"w": wxyz[0], "x": wxyz[1], "y": wxyz[2], "z": wxyz[3]}
