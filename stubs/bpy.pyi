"""
Type stubs for Blender Python API (bpy)
These stubs provide type hints for development outside of Blender
"""

from typing import Any, Dict, List, Optional, Union
from mathutils import Vector, Matrix, Euler, Quaternion

class Object:
    name: str
    type: str
    location: Vector
    rotation_euler: Euler
    scale: Vector
    animation_data: Optional['AnimData']
    
    def select_set(self, state: bool) -> None: ...

class Scene:
    frame_start: int
    frame_end: int
    frame_current: int
    objects: 'Collection[Object]'
    
    def frame_set(self, frame: int) -> None: ...

class Context:
    scene: Scene
    active_object: Optional[Object]
    selected_objects: List[Object]
    view_layer: 'ViewLayer'
    collection: 'Collection'

class Collection:
    objects: 'ObjectCollection'

class ObjectCollection:
    def link(self, obj: Object) -> None: ...

class ViewLayer:
    objects: 'LayerObjects'

class LayerObjects:
    active: Optional[Object]

class AnimData:
    action: Optional['Action']

class Action:
    fcurves: List['FCurve']

class FCurve:
    select: bool

class Bone:
    select: bool

class PoseBone:
    bone: Bone
    rotation_euler: Euler
    location: Vector
    scale: Vector
    
    def keyframe_insert(self, data_path: str, frame: Optional[int] = None) -> None: ...

class PoseBones:
    def __getitem__(self, key: str) -> PoseBone: ...
    def __contains__(self, key: str) -> bool: ...

class Armature:
    pose: 'Pose'

class Pose:
    bones: PoseBones

class Ops:
    object: 'ObjectOps'
    mesh: 'MeshOps'
    pose: 'PoseOps'
    import_scene: 'ImportOps'
    export_scene: 'ExportOps'

class ObjectOps:
    def select_all(self, action: str = 'SELECT') -> None: ...
    def delete(self, use_global: bool = False, confirm: bool = True) -> None: ...
    def camera_add(self, location: tuple = (0, 0, 0)) -> None: ...
    def light_add(self, type: str = 'POINT', location: tuple = (0, 0, 0)) -> None: ...
    def armature_add(self, location: tuple = (0, 0, 0)) -> None: ...
    def mode_set(self, mode: str = 'OBJECT') -> None: ...

class MeshOps:
    def primitive_cube_add(self, size: float = 2.0, location: tuple = (0, 0, 0)) -> None: ...

class PoseOps:
    def select_all(self, action: str = 'SELECT') -> None: ...

class ImportOps:
    def fbx(self, filepath: str) -> None: ...
    def obj(self, filepath: str) -> None: ...

class ExportOps:
    def fbx(self, filepath: str, use_selection: bool = True, 
            bake_anim: bool = True, bake_anim_use_all_bones: bool = True,
            bake_anim_use_nla_strips: bool = False, 
            bake_anim_use_all_actions: bool = False) -> None: ...
    def gltf(self, filepath: str, use_selection: bool = True,
             export_animations: bool = True) -> None: ...

class Data:
    libraries: 'Libraries'

class Libraries:
    def load(self, filepath: str) -> 'LibraryContext': ...

class LibraryContext:
    def __enter__(self) -> tuple: ...
    def __exit__(self, *args) -> None: ...

# Module-level objects
context: Context
ops: Ops
data: Data

# Type aliases
types = type('types', (), {
    'Object': Object,
    'Scene': Scene,
    'Context': Context,
})()
