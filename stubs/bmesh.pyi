"""
Type stubs for Blender bmesh module
These stubs provide type hints for development outside of Blender
"""

from typing import Any, List, Optional, Iterator
from mathutils import Vector

class BMesh:
    """Blender mesh data structure"""
    verts: 'BMVertSeq'
    edges: 'BMEdgeSeq'
    faces: 'BMFaceSeq'
    
    def clear(self) -> None: ...
    def free(self) -> None: ...
    def copy(self) -> 'BMesh': ...
    def normal_update(self) -> None: ...
    def ensure_face_index(self) -> None: ...

class BMVert:
    """Blender mesh vertex"""
    co: Vector
    normal: Vector
    index: int
    select: bool
    hide: bool
    
    def calc_edge_angle(self, fallback: Optional[float] = None) -> float: ...

class BMEdge:
    """Blender mesh edge"""
    verts: List[BMVert]
    index: int
    select: bool
    hide: bool
    
    def calc_length(self) -> float: ...

class BMFace:
    """Blender mesh face"""
    verts: List[BMVert]
    edges: List[BMEdge]
    normal: Vector
    index: int
    select: bool
    hide: bool
    
    def calc_area(self) -> float: ...
    def calc_center_median(self) -> Vector: ...

class BMVertSeq:
    """Sequence of vertices"""
    def new(self, co: Vector) -> BMVert: ...
    def remove(self, vert: BMVert) -> None: ...
    def __iter__(self) -> Iterator[BMVert]: ...
    def __len__(self) -> int: ...

class BMEdgeSeq:
    """Sequence of edges"""
    def new(self, verts: List[BMVert]) -> BMEdge: ...
    def remove(self, edge: BMEdge) -> None: ...
    def __iter__(self) -> Iterator[BMEdge]: ...
    def __len__(self) -> int: ...

class BMFaceSeq:
    """Sequence of faces"""
    def new(self, verts: List[BMVert]) -> BMFace: ...
    def remove(self, face: BMFace) -> None: ...
    def __iter__(self) -> Iterator[BMFace]: ...
    def __len__(self) -> int: ...

# Module functions
def new() -> BMesh: ...
def from_mesh(mesh: Any) -> BMesh: ...

# Submodules
class ops:
    """BMesh operators"""
    @staticmethod
    def subdivide_edges(bm: BMesh, edges: List[BMEdge], 
                       cuts: int = 1, use_grid_fill: bool = True) -> None: ...
    
    @staticmethod
    def extrude_face_region(bm: BMesh, faces: List[BMFace]) -> None: ...
    
    @staticmethod
    def inset_faces(bm: BMesh, faces: List[BMFace], 
                   thickness: float = 0.01, depth: float = 0.0) -> None: ...

class utils:
    """BMesh utilities"""
    @staticmethod
    def face_split_edgenet(face: BMFace, edges: List[BMEdge]) -> List[BMFace]: ...
    
    @staticmethod
    def edge_split(edge: BMEdge, vert: BMVert, factor: float) -> BMVert: ...
