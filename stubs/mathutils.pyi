"""
Type stubs for Blender mathutils module
These stubs provide type hints for development outside of Blender
"""

from typing import Union, Tuple, List, Iterator, overload

class Vector:
    """3D Vector class"""
    x: float
    y: float
    z: float
    w: float  # For 4D vectors
    
    def __init__(self, seq: Union[Tuple[float, ...], List[float]] = (0.0, 0.0, 0.0)) -> None: ...
    
    def __add__(self, other: 'Vector') -> 'Vector': ...
    def __sub__(self, other: 'Vector') -> 'Vector': ...
    def __mul__(self, scalar: float) -> 'Vector': ...
    def __truediv__(self, scalar: float) -> 'Vector': ...
    def __neg__(self) -> 'Vector': ...
    
    def __getitem__(self, index: int) -> float: ...
    def __setitem__(self, index: int, value: float) -> None: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[float]: ...
    
    @property
    def length(self) -> float: ...
    
    @property
    def length_squared(self) -> float: ...
    
    def normalize(self) -> None: ...
    def normalized(self) -> 'Vector': ...
    def dot(self, other: 'Vector') -> float: ...
    def cross(self, other: 'Vector') -> 'Vector': ...
    def angle(self, other: 'Vector') -> float: ...
    def copy(self) -> 'Vector': ...
    def zero(self) -> None: ...

class Matrix:
    """4x4 Matrix class"""
    
    def __init__(self, rows: List[List[float]] = None) -> None: ...
    
    def __mul__(self, other: Union['Matrix', 'Vector']) -> Union['Matrix', 'Vector']: ...
    def __getitem__(self, index: int) -> List[float]: ...
    def __setitem__(self, index: int, value: List[float]) -> None: ...
    
    @property
    def translation(self) -> Vector: ...
    
    @translation.setter
    def translation(self, value: Vector) -> None: ...
    
    def copy(self) -> 'Matrix': ...
    def identity(self) -> None: ...
    def invert(self) -> None: ...
    def inverted(self) -> 'Matrix': ...
    def transpose(self) -> None: ...
    def transposed(self) -> 'Matrix': ...
    def decompose(self) -> Tuple[Vector, 'Quaternion', Vector]: ...
    
    @classmethod
    def Identity(cls, size: int = 4) -> 'Matrix': ...
    
    @classmethod
    def Translation(cls, vector: Vector) -> 'Matrix': ...
    
    @classmethod
    def Rotation(cls, angle: float, size: int, axis: Union[str, Vector]) -> 'Matrix': ...
    
    @classmethod
    def Scale(cls, factor: Union[float, Vector], size: int = 4) -> 'Matrix': ...

class Euler:
    """Euler rotation class"""
    x: float
    y: float
    z: float
    order: str
    
    def __init__(self, angles: Union[Tuple[float, float, float], List[float]] = (0.0, 0.0, 0.0),
                 order: str = 'XYZ') -> None: ...
    
    def __getitem__(self, index: int) -> float: ...
    def __setitem__(self, index: int, value: float) -> None: ...
    def __iter__(self) -> Iterator[float]: ...
    
    def copy(self) -> 'Euler': ...
    def zero(self) -> None: ...
    def to_matrix(self) -> Matrix: ...
    def to_quaternion(self) -> 'Quaternion': ...

class Quaternion:
    """Quaternion rotation class"""
    w: float
    x: float
    y: float
    z: float
    
    def __init__(self, seq: Union[Tuple[float, float, float, float], List[float]] = (1.0, 0.0, 0.0, 0.0)) -> None: ...
    
    def __mul__(self, other: Union['Quaternion', Vector]) -> Union['Quaternion', Vector]: ...
    def __getitem__(self, index: int) -> float: ...
    def __setitem__(self, index: int, value: float) -> None: ...
    def __iter__(self) -> Iterator[float]: ...
    
    @property
    def magnitude(self) -> float: ...
    
    def copy(self) -> 'Quaternion': ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'Quaternion': ...
    def conjugate(self) -> None: ...
    def conjugated(self) -> 'Quaternion': ...
    def invert(self) -> None: ...
    def inverted(self) -> 'Quaternion': ...
    def to_matrix(self) -> Matrix: ...
    def to_euler(self, order: str = 'XYZ') -> Euler: ...
    def to_axis_angle(self) -> Tuple[Vector, float]: ...
    
    @classmethod
    def Identity(cls) -> 'Quaternion': ...

class Color:
    """Color class"""
    r: float
    g: float
    b: float
    
    def __init__(self, rgb: Union[Tuple[float, float, float], List[float]] = (0.0, 0.0, 0.0)) -> None: ...
    
    def __getitem__(self, index: int) -> float: ...
    def __setitem__(self, index: int, value: float) -> None: ...
    def __iter__(self) -> Iterator[float]: ...
    
    def copy(self) -> 'Color': ...

# Utility functions
def lerp(a: float, b: float, factor: float) -> float: ...

# Noise functions
class noise:
    @staticmethod
    def random() -> float: ...
    
    @staticmethod
    def seed_set(seed: int) -> None: ...
